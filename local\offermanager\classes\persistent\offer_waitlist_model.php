<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace local_offermanager\persistent;

use core\persistent;
use local_audit\traits\audit_trait;
use moodle_exception;

defined('MOODLE_INTERNAL') || die();

/**
 * Class offer_waitlist_model
 *
 * @package    local_offermanager
 * @copyright  2025 REVVO <www.revvo.com.br>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class offer_waitlist_model extends persistent
{
    use audit_trait {
        before_update as protected audit_before_update;
        after_update as protected audit_after_update;
        after_create as protected audit_after_create;
        before_delete as protected audit_before_delete;
        after_delete as protected audit_after_delete;
    }

    /** Nome da tabela no banco de dados */
    const TABLE = 'local_offermanager_waitlist';

    /**
     * Define a estrutura do objeto.
     *
     * @return array
     */
    protected static function define_properties()
    {
        return [
            'offerclassid' => [
                'type' => PARAM_INT,
                'description' => 'ID da turma da oferta',
                'null' => NULL_NOT_ALLOWED,
            ],
            'userid' => [
                'type' => PARAM_INT,
                'description' => 'ID do usuário na lista de espera',
                'null' => NULL_NOT_ALLOWED,
            ],
            'managerid' => [
                'type' => PARAM_INT,
                'description' => 'ID do gestor responsável',
                'null' => NULL_NOT_ALLOWED,
            ],
            'companyid' => [
                'type' => PARAM_INT,
                'description' => 'ID da concessionária',
                'null' => NULL_NOT_ALLOWED,
            ],
            'usercreated' => [
                'type' => PARAM_INT,
                'description' => 'User id do criador',
                'null' => NULL_NOT_ALLOWED,
                'default' => function () {
                    global $USER;
                    return $USER->id;
                }
            ]
        ];
    }

    /**
     * Retrieves a list of users from the waiting list of an offer class.
     *
     * @param int $offerclassid ID of the offer class
     * @param string $searchstring Search term for the user's name, manager's name or company name
     * @param string $fieldstring Field to search in. Accepted values are 'name', 'manager' or 'company'
     * @param array $excludeids Array of user ids to exclude from the result
     * @param bool $limit Whether to limit the result to 200 records
     *
     * @return array Array of objects containing the user id and fullname
     */
    public static function get_list(
        int $offerclassid,
        string $searchstring = '',
        string $fieldstring = 'name',
        array $excludeids = [],
        bool $limit = false
    ) {
        global $DB;

        $search_fields = [
            'name' => $DB->sql_concat('user.firstname', "' '", 'user.lastname'),
            'manager' => $DB->sql_concat('manager.firstname', "' '", 'manager.lastname'),
            'company' => 'company.name'
        ];

        $id_fields = [
            'name' => 'user.id',
            'manager' => 'manager.id',
            'company' => 'company.id'
        ];

        $fullname_fields = [
            'name' => $DB->sql_concat('user.firstname', "' '", 'user.lastname'),
            'manager' => $DB->sql_concat('manager.firstname', "' '", 'manager.lastname'),
            'company' => "company.name"
        ];

        $search_field = $search_fields[$fieldstring] ?? $search_fields['name'];
        $id_field = $id_fields[$fieldstring] ?? $id_fields['name'];
        $fullname_field = $fullname_fields[$fieldstring] ?? $search_fields['name'];

        $params = [$offerclassid];

        $conditions = 'mlow.offerclassid = ?';

        if ($searchstring) {
            $like = $DB->sql_like($search_field, '?', false, false);
            $conditions .= " AND {$like}";
            $params[] = '%' . $searchstring . '%';
        }

        if (!empty($excludeids)) {
            list($insql, $inparams) = $DB->get_in_or_equal($excludeids, SQL_PARAMS_QM, '?', false);
            $conditions .= ' AND mlow.userid ' . $insql;
            $params = array_merge($params, $inparams);
        }

        $limit = $limit ? 'LIMIT 0, 200' : '';

        $sql = "SELECT
                    DISTINCT {$id_field} as id,
                    {$fullname_field} as fullname
                FROM {local_offermanager_waitlist} mlow
                    JOIN {user} user ON (user.id = mlow.userid)
                    JOIN {user} manager ON (manager.id = mlow.managerid)
                    JOIN {local_hierarchy_dealership} company ON (company.id = mlow.companyid)
                WHERE {$conditions}
                ORDER BY user.firstname, user.lastname
                {$limit}";

        return $DB->get_records_sql($sql, $params);
    }

    /**
     * Retrieves a filtered list of waitlist records, paginated.
     *
     * The list is filtered by the specified filters and ordered by the
     * specified column and direction.
     *
     * @param int $offerclassid offer class ID
     * @param array $filters filters to apply to the list
     *      - userids: array of user IDs to filter by
     *      - managerids: array of manager IDs to filter by
     *      - companyids: array of company IDs to filter by
     * @param int $page page number to retrieve (1-based)
     * @param int $perpage number of records per page
     * @param string $orderby column to order by
     * @param string $direction direction to order by (ASC|DESC)
     *
     * @return array
     *      - page: current page number
     *      - per_page: number of records per page
     *      - total: total number of records matching the filters
     *      - waitlist: array of offer_waitlist_model objects
     */
    public static function get_filtered_list(
        int $offerclassid,
        array $filters = [],
        int $page = 0,
        int $perpage = 20,
        string $orderby = 'fullname',
        string $direction = 'ASC'
    ): array {
        global $DB;

        $wheres = ['mlow.offerclassid = ?'];
        $params = [$offerclassid];

        if (!empty($filters['userids'])) {
            list($inuseridsql, $inuseridparams) = $DB->get_in_or_equal($filters['userids']);
            $wheres[] = "mlow.userid $inuseridsql";
            $params = array_merge($params, $inuseridparams);
        }

        if (!empty($filters['managerids'])) {
            list($inmanageridsql, $inmanageridparams) = $DB->get_in_or_equal($filters['managerids']);
            $wheres[] = "mlow.managerid $inmanageridsql";
            $params = array_merge($params, $inmanageridparams);
        }

        if (!empty($filters['companyids'])) {
            list($incompanyidsql, $incompanyidparams) = $DB->get_in_or_equal($filters['companyids']);
            $wheres[] = "mlow.companyid $incompanyidsql";
            $params = array_merge($params, $incompanyidparams);
        }

        $select = implode(' AND ', $wheres);
        $page = max(0, $page);
        $offset = max(0, $page * $perpage);

        $total = $DB->count_records_sql(
            "SELECT COUNT(1)
            FROM {local_offermanager_waitlist} mlow
            WHERE {$select}",
            $params
        );

        $orderby_relationship = self::get_orderby_relationship($orderby);
        $orderby_relationship = $DB->sql_order_by_text($orderby_relationship);

        $direction = strtoupper($direction) === 'DESC' ? 'DESC' : 'ASC';

        $waitlist_ids = $DB->get_fieldset_sql(
            "SELECT
                mlow.id
            FROM {local_offermanager_waitlist} mlow
                JOIN {user} user ON (user.id = mlow.userid)
                JOIN {user} manager ON (manager.id = mlow.managerid)
                JOIN {local_hierarchy_dealership} company ON (company.id = mlow.companyid)
            WHERE {$select}
            ORDER BY {$orderby_relationship} {$direction}
            LIMIT {$perpage}
            OFFSET {$offset}",
            $params
        );

        $waitlist = array_map(
            function ($id) {
                return new offer_waitlist_model($id);
            },
            $waitlist_ids
        );

        return [
            'page' => $page,
            'per_page' => $perpage,
            'total' => $total,
            'waitlist' => $waitlist
        ];
    }

    public static function get_orderby_relationship(string $field): string
    {
        global $DB;

        $relationships = [
            'fullname' => $DB->sql_concat('user.firstname', "' '", 'user.lastname'),
            'companyname' => 'company.name',
            'managername' => $DB->sql_concat('manager.firstname', "' '", 'manager.lastname'),
            'managerphone' => 'manager.phone1',
            'managermail' => 'manager.email',
            'timecreated' => 'mlow.timecreated'
        ];

        return $relationships[$field] ?? throw new moodle_exception('error:invalid_orderby_field', 'local_offermanager', $field);
    }

    /**
     * Retorna os registros da lista de espera para uma turma específica.
     *
     * @param int $offerclassid ID da turma
     * @param string $sort_by Campo para ordenação
     * @param string $sort_direction Direção da ordenação (ASC/DESC)
     * @return offer_waitlist_model[]
     */
    public static function get_by_offer_class(
        int $offerclassid,
        string $sort_by = 'timecreated',
        string $sort_direction = 'ASC'
    ): array {
        return self::get_records(
            ['offerclassid' => $offerclassid],
            "{$sort_by} {$sort_direction}"
        );
    }

    /**
     * Retorna os registros da lista de espera para um usuário específico.
     *
     * @param int $userid ID do usuário
     * @param string $sort_by Campo para ordenação
     * @param string $sort_direction Direção da ordenação (ASC/DESC)
     * @return offer_waitlist_model[]
     */
    public static function get_by_user(
        int $userid,
        string $sort_by = 'timecreated',
        string $sort_direction = 'ASC'
    ): array {
        return self::get_records(
            ['userid' => $userid],
            "{$sort_by} {$sort_direction}"
        );
    }

    /**
     * Verifica se um usuário já está na lista de espera de uma turma.
     *
     * @param int $offerclassid ID da turma
     * @param int $userid ID do usuário
     * @return bool
     */
    public static function user_is_in_waitlist(int $offerclassid, int $userid): bool
    {
        return self::record_exists_select(
            'offerclassid = :offerclassid AND userid = :userid',
            [
                'offerclassid' => $offerclassid,
                'userid' => $userid
            ]
        );
    }

    /**
     * Conta quantos usuários estão na lista de espera de uma turma.
     *
     * @param int $offerclassid ID da turma
     * @return int
     */
    public static function count_by_offer_class(int $offerclassid): int
    {
        return self::count_records(['offerclassid' => $offerclassid]);
    }

    /**
     * Remove um usuário da lista de espera de uma turma específica.
     *
     * @param int $offerclassid ID da turma
     * @param int $userid ID do usuário
     * @return bool
     * @throws moodle_exception
     */
    public static function remove_user_from_waitlist(int $offerclassid, int $userid): bool
    {
        $record = self::get_record([
            'offerclassid' => $offerclassid,
            'userid' => $userid
        ]);

        if (!$record) {
            throw new moodle_exception('error:user_not_in_waitlist', 'local_offermanager');
        }

        return $record->delete();
    }

    /**
     * Hook executado após a criação do registro.
     */
    protected function after_create()
    {
        $this->audit_after_create();
    }

    /**
     * Hook executado antes da atualização do registro.
     */
    protected function before_update()
    {
        $this->audit_before_update();
    }

    /**
     * Hook executado após a atualização do registro.
     *
     * @param bool $result
     */
    protected function after_update($result)
    {
        if ($result) {
            $this->audit_after_update($result);
        }
    }

    /**
     * Hook executado antes da exclusão do registro.
     */
    protected function before_delete()
    {
        $this->audit_before_delete();
    }

    /**
     * Hook executado após a exclusão do registro.
     *
     * @param bool $result
     */
    protected function after_delete($result)
    {
        if ($result) {
            $this->audit_after_delete($result);
        }
    }

    /**
     * Obtém o próximo usuário na lista de espera de uma turma.
     *
     * @param int $offerclassid ID da turma
     * @return offer_waitlist_model|false
     */
    public static function get_next_in_waitlist(int $offerclassid)
    {
        $records = self::get_by_offer_class($offerclassid, 'timecreated', 'ASC');

        return !empty($records) ? reset($records) : false;
    }

    /**
     * Limpa toda a lista de espera de uma turma.
     *
     * @param int $offerclassid ID da turma
     * @return int Número de registros removidos
     */
    public static function clear_waitlist(int $offerclassid): int
    {
        $records = self::get_by_offer_class($offerclassid);
        $removed_count = 0;

        foreach ($records as $record) {
            if ($record->delete()) {
                $removed_count++;
            }
        }

        return $removed_count;
    }

    /**
     * Obtém estatísticas da lista de espera por concessionária.
     *
     * @param int $offerclassid ID da turma
     * @return array
     */
    public static function get_waitlist_stats_by_company(int $offerclassid): array
    {
        global $DB;

        $sql = "SELECT w.companyid, COUNT(*) as total
                FROM {" . self::TABLE . "} w
                WHERE w.offerclassid = :offerclassid
                GROUP BY w.companyid
                ORDER BY total DESC";

        $params = ['offerclassid' => $offerclassid];

        return $DB->get_records_sql($sql, $params);
    }

    /**
     * Verifica se pode adicionar um usuário à lista de espera.
     *
     * @param int $offerclassid ID da turma
     * @param int $userid ID do usuário
     * @return bool
     */
    public static function can_add_user_to_waitlist(int $offerclassid, int $userid): bool
    {
        // Verifica se o usuário já está na lista de espera
        if (self::user_is_in_waitlist($offerclassid, $userid)) {
            return false;
        }

        return true;
    }
}
