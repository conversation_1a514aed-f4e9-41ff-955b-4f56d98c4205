/**
 * Format a raw phone number string.
 *
 * This function takes a string of digits and converts it into a phone number
 * string in the format "+DDI (DDD) XXXX-XXXX".
 *
 * @param {string} raw - The raw phone number string.
 * @returns {string} The formatted phone number string.
 */
export function formatPhoneNumber(raw) {
  const digits = raw.replace(/\D/g, "");

  let ddi = "";
  let ddd = "";
  let number = "";

  if (digits.length <= 8) return digits;

  if (digits.length > 11) {
    ddi = "+" + digits.slice(0, digits.length - 11);
    ddd = digits.slice(-11, -9);
    number = digits.slice(-9);
  } else if (digits.length === 11) {
    ddd = digits.slice(0, 2);
    number = digits.slice(2);
  } else if (digits.length === 10) {
    ddd = digits.slice(0, 2);
    number = digits.slice(2);
  } else {
    number = digits;
  }

  let formattedNumber = "";
  if (number.length === 9) {
    formattedNumber = number.replace(/(\d{5})(\d{4})/, "$1-$2");
  } else if (number.length === 8) {
    formattedNumber = number.replace(/(\d{4})(\d{4})/, "$1-$2");
  } else {
    formattedNumber = number;
  }

  return [ddi, ddd ? `(${ddd})` : "", formattedNumber]
    .filter(Boolean)
    .join(" ");
}
