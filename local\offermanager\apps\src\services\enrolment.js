import { ajax } from "@/helpers/moodle";

/**
 * Busca matrículas de uma turma.
 * @param {object} params
 * @returns {Promise<any>}
 */
export async function fetchEnrolments(params = {}) {
  try {
    return await ajax("local_offermanager_fetch_enrolments", {
      offerclassid: params.offerclassid,
      userids: params.userids || [],
      page: params.page || 1,
      perpage: params.perpage || 20,
      orderby: params.orderby || "fullname",
      direction: params.direction || "ASC",
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Busca usuários matriculados em uma turma com filtros.
 * @param {object} params
 * @returns {Promise<any>}
 */
export async function searchEnrolledUsers(params = {}) {
  try {
    return await ajax("local_offermanager_get_enroled_users", {
      offerclassid: params.offerclassid,
      searchstring: params.searchstring || "",
      fieldstring: params.fieldstring || "name",
      excludeduserids: params.excludeduserids || [],
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Matricula usuários em uma turma.
 * @param {object} params
 * @returns {Promise<Array|Object>} Array de resultados de matrícula ou objeto com propriedade data
 */
export async function enrolUsers(params = {}) {
  try {
    return await ajax("local_offermanager_enrol_users", {
      offerclassid: params.offerclassid,
      userids: params.userids || [],
      roleid: params.roleid || 5,
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Busca usuários potenciais para matrícula em uma turma.
 * @param {number} offerclassid ID da turma
 * @returns {Promise<Array>} Array de usuários potenciais
 */
export async function getPotentialUsersToEnrol(
  offerclassid,
  searchString = "",
  excludedUserids
) {
  try {
    return await ajax("local_offermanager_get_potential_users_to_enrol", {
      offerclassid: offerclassid,
      search_string: searchString,
      excluded_userids: excludedUserids,
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Edita a matrícula de um usuário.
 * @param {object} params Parâmetros para edição da matrícula
 * @param {number} params.offeruserenrolid ID da matrícula do usuário
 * @param {number} params.status Status da matrícula (0=inativo, 1=ativo)
 * @param {number} params.timestart Timestamp de início da matrícula
 * @param {number} params.timeend Timestamp de fim da matrícula
 * @param {number} params.roleid ID do papel do usuário
 * @returns {Promise<boolean>} Indica se a edição foi bem-sucedida
 */
export async function editEnrolment(params = {}) {
  try {
    return await ajax("local_offermanager_edit_offer_user_enrol", {
      offeruserenrolid: params.offeruserenrolid,
      status: params.status,
      timestart: params.timestart,
      timeend: params.timeend,
      roleid: params.roleid,
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Exclui a matrícula de um usuário.
 * @param {number} offeruserenrolid ID da matrícula do usuário
 * @returns {Promise<boolean>} Indica se a exclusão foi bem-sucedida
 */
export async function deleteEnrolment(offeruserenrolid) {
  try {
    return await deleteEnrolmentBulk([offeruserenrolid]).then((results) => {});
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Edita matrículas em lote.
 * @param {object} params Parâmetros para edição das matrículas
 * @param {Array<number>} params.offeruserenrolids Array de IDs das matrículas dos usuários
 * @param {number} params.status Status da matrícula (0=inativo, 1=ativo)
 * @param {number} params.timestart Timestamp de início da matrícula
 * @param {number} params.timeend Timestamp de fim da matrícula
 * @returns {Promise<Array>} Array com os resultados das edições
 */
export async function editEnrolmentBulk(params = {}) {
  try {
    return await ajax("local_offermanager_edit_offer_user_enrol_bulk", {
      offeruserenrolids: params.offeruserenrolids || [],
      status: params.status,
      timestart: params.timestart,
      timeend: params.timeend,
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Exclui matrículas em lote.
 * @param {Array<number>} offeruserenrolids Array de IDs das matrículas dos usuários
 * @returns {Promise<Array>} Array com os resultados das exclusões
 */
export async function deleteEnrolmentBulk(offeruserenrolids) {
  try {
    const response = await ajax(
      "local_offermanager_delete_offer_user_enrol_bulk",
      {
        offeruserenrolids: offeruserenrolids,
      }
    );

    if (response === true) {
      // Criar um array de resultados com status de sucesso para todas as matrículas
      const results = offeruserenrolids.map((id) => ({
        id: id,
        operation_status: true,
      }));

      return results;
    }

    return [];
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Busca os papéis de um usuário específico em uma turma.
 * @param {number} offeruserenrolid ID da matrícula do usuário
 * @returns {Promise<Array>} Array de papéis do usuário
 */
export async function getUserRoles(offeruserenrolid) {
  try {
    return await ajax("local_offermanager_get_roles", {
      offeruserenrolid: offeruserenrolid,
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Atualiza os papéis de um usuário em uma turma.
 * @param {number} offeruserenrolid ID da matrícula do usuário
 * @param {Array<number>} roleids Array de IDs dos papéis
 * @returns {Promise<boolean>} Indica se a atualização foi bem-sucedida
 */
export async function updateUserRoles(offeruserenrolid, roleids) {
  try {
    return await ajax("local_offermanager_update_roles", {
      offeruserenrolid: offeruserenrolid,
      roleids: Array.isArray(roleids) ? roleids : [roleids],
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Busca usuários na lista de espera de uma turma.
 * @param {object} params
 * @returns {Promise<any>}
 */
export async function fetchWaitingListUsers(params = {}) {
  try {
    return await ajax("local_offermanager_fetch_waiting_list_users", {
      offerclassid: params.offerclassid,
      filters: params.filters || [],
      page: params.page || 1,
      perpage: params.perpage || 20,
      orderby: params.orderby || "fullname",
      direction: params.direction || "ASC",
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Busca usuários matriculados em uma turma com filtros.
 * @param {object} params
 * @returns {Promise<any>}
 */
export async function searchWaitingListUsers(params = {}) {
  try {
    return await ajax("local_offermanager_get_waiting_list_users", {
      offerclassid: params.offerclassid,
      searchstring: params.searchstring || "",
      fieldstring: params.fieldstring || "name",
      excludeids: params.excludeids || [],
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}

/**
 * Remove um usuário da lista de espera de uma turma.
 * @param {object} params
 * @returns {Promise<any>}
 */
export async function removeFromWaitingList(params = {}) {
  try {
    return await ajax("local_offermanager_remove_from_waiting_list", {
      offerclassid: params.offerclassid,
      userid: params.userid,
    });
  } catch (error) {
    throw error; // Re-throw the error so you can handle it in the component
  }
}
