<template>
  <div id="enrollments-view">
    <PageHeader title="Usuários">
      <template #actions>
        <BackButton @click="navigateToBack" />
      </template>
    </PageHeader>

    <PageTabs :tabs="tabs" />

    <!-- Filtros -->
    <div class="filters-section mb-3">
      <div class="row">
        <div class="col-xl-8">
          <div class="row">
            <!-- Filtro por Nome -->
            <div class="col-md-4">
              <div class="form-group">
                <Autocomplete
                  :items="nameOptions"
                  placeholder="Buscar..."
                  label="Filtrar por nome"
                  :has-search-icon="true"
                  :auto-open="false"
                  :min-chars="3"
                  :loading="filteringByName"
                  :no-results-text="'Nenhum registro encontrado'"
                  @update:modelValue="selectNameOption"
                  @search="(search) => getNameOptions(search)"
                />
              </div>
            </div>

            <!-- Filtro por CPF -->
            <div class="col-md-4 mt-3 mt-md-0">
              <div class="form-group">
                <Autocomplete
                  :items="managerOptions"
                  placeholder="Buscar..."
                  label="Filtrar por gestor"
                  :has-search-icon="true"
                  :auto-open="false"
                  :min-chars="3"
                  :loading="filteringByCpf"
                  :no-results-text="'Nenhum registro encontrado'"
                  @update:modelValue="selectCpfOption"
                  @search="(search) => getManagerOptions(search)"
                />
              </div>
            </div>

            <!-- Filtro por E-mail -->
            <div class="col-md-4 mt-3 mt-md-0">
              <div class="form-group">
                <Autocomplete
                  :items="companyOptions"
                  placeholder="Buscar..."
                  label="Filtrar por concessionária"
                  :has-search-icon="true"
                  :auto-open="false"
                  :min-chars="3"
                  :loading="filteringByEmail"
                  :no-results-text="'Nenhum registro encontrado'"
                  @update:modelValue="selectEmailOption"
                  @search="(search) => getCompanyOptions(search)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <FilterTags>
      <FilterTag
        v-for="filter in filters"
        :key="user.id"
        @remove="removeFilter(filter.id)"
      >
        <span v-if="filter.type === 'manager'">
          Gestor: {{ filter.label }}
        </span>
        <span v-else-if="filter.type === 'company'">
          Concessionária: {{ filter.label }}
        </span>
        <span v-else>{{ filter.label }}</span>
      </FilterTag>
    </FilterTags>

    <!-- Botão Limpar Filtros -->
    <div v-if="filters.length > 0" class="my-4">
      <CustomButton variant="secondary" label="Limpar" @click="clearfilters" />
    </div>

    <!-- Mensagem de Erro -->
    <div class="alert alert-danger" v-if="error">
      <i class="fas fa-exclamation-circle"></i>
      {{ error }}
    </div>

    <div class="my-3">{{ totalDataInfo }}</div>

    <CustomTable
      :headers="tableHeaders"
      :items="data"
      tableClass="table-hover"
      :sort-by="sortBy"
      :sort-desc="sortDesc"
      @sort="handleTableSort"
    >
      <template #empty-state>
        <div class="empty-state">
          <span class="no-results">
            {{
              loading
                ? "Carregando registros..."
                : "Nenhum registro encontrado..."
            }}
          </span>
        </div>
      </template>

      <template #item-fullName="{ item }">
        <a
          class="user-name-container"
          :href="`/user/view.php?id=${item.userid}`"
          :title="'Ver perfil de ' + item.fullName"
        >
          <UserAvatar :full-name="item.fullName" :size="36" />
          <span class="user-name-link">{{ item.fullName }}</span>
        </a>
      </template>
      <template #item-managerMail="{ item }">
        <a :href="`mailto:${item.managerMail}`">{{ item.managerMail }}</a>
      </template>

      <template #item-actions="{ item }">
        <div class="action-buttons">
          <button
            class="btn-action btn-delete"
            @click="requestRemoveUser(item.userid)"
            title="Remover da lista de espera"
          >
            <i class="fa fa-trash fa-fw"></i>
          </button>
        </div>
      </template>
    </CustomTable>

    <!-- Paginação -->
    <Pagination
      v-model:current-page="currentPage"
      v-model:per-page="perPage"
      :total="total"
      :loading="loading"
    />

    <ConfirmationModal
      size="md"
      :show="showRemoveUserModal"
      title="Remover usuário da lista de espera"
      message="Tem certeza de que deseja remover este usuário da lista de espera? Ao remover, o usuário perde a sua posição na fila."
      confirm-button-text="Remover usuário"
      cancel-button-text="Cancelar"
      icon="warning"
      @close="showRemoveUserModal = false"
      @confirm="removeUser"
    />

    <LFLoading :is-loading="loading" />

    <!-- Toast para mensagens -->
    <Toast
      :show="showToast"
      :message="toastMessage"
      :type="toastType"
      :duration="3000"
    />
  </div>
</template>

<script>
import { getClass } from "@/services/offer";
import {
  fetchWaitingListUsers,
  searchWaitingListUsers,
  removeFromWaitingList,
} from "@/services/enrolment";

import { formatPhoneNumber } from "@/helpers/phone";

import Toast from "@/components/Toast.vue";
import ToastMessages from "@/mixins/toastMessages";

import CircleInfoIcon from "@/assets/icons/circle-info.svg";

// Importação dos componentes
import PageTabs from "@/components/PageTabs.vue";
import LFLoading from "@/components/LFLoading.vue";
import FilterTag from "@/components/FilterTag.vue";
import FilterRow from "@/components/FilterRow.vue";
import FilterTags from "@/components/FilterTags.vue";
import Pagination from "@/components/Pagination.vue";
import PageHeader from "@/components/PageHeader.vue";
import BackButton from "@/components/BackButton.vue";
import UserAvatar from "@/components/UserAvatar.vue";
import FilterGroup from "@/components/FilterGroup.vue";
import CustomLabel from "@/components/CustomLabel.vue";
import CustomTable from "@/components/CustomTable.vue";
import CustomInput from "@/components/CustomInput.vue";
import CustomButton from "@/components/CustomButton.vue";
import CustomSelect from "@/components/CustomSelect.vue";
import Autocomplete from "@/components/Autocomplete.vue";
import FilterSection from "@/components/FilterSection.vue";
import FilterActions from "@/components/FilterActions.vue";
import CustomCheckbox from "@/components/CustomCheckbox.vue";
import ConfirmationModal from "@/components/ConfirmationModal.vue";

export default {
  name: "WaitingList",

  mixins: [ToastMessages],

  components: {
    CircleInfoIcon,
    PageTabs,
    CustomLabel,
    CustomTable,
    CustomSelect,
    CustomInput,
    CustomCheckbox,
    CustomButton,
    FilterSection,
    FilterRow,
    FilterGroup,
    FilterActions,
    FilterTag,
    FilterTags,
    Pagination,
    PageHeader,
    ConfirmationModal,
    Autocomplete,
    Toast,
    BulkDeleteWaitingListModal,
    BackButton,
    UserAvatar,
    LFLoading,
  },

  props: {
    offerClassId: {
      type: [Number, String],
      required: true,
    },
  },

  data() {
    return {
      nameOptions: [],
      managerOptions: [],
      companyOptions: [],

      tabs: [
        {
          title: "Usuários inscritos",
          value: "enrolled",
          route: "offer.class.enrollments",
        },
        {
          title: "Lista de espera",
          value: "waitlist",
          route: "offer.class.waiting-list",
        },
        {
          title: "Inscrições revogadas",
          value: "revoked",
          route: "offer.class.revoked-enrollments",
        },
      ],

      roleOptions: [],

      tableHeaders: [
        { text: "Nº", value: "order", sortable: false, width: "50px" },
        { text: "NOME/SOBRENOME", value: "fullName", sortable: true },
        { text: "CONCESSIONÁRIA", value: "companyName", sortable: true },
        { text: "NOME DO GESTOR", value: "managerName", sortable: true },
        { text: "TELEFONE DO GESTOR", value: "managerPhone", sortable: false },
        { text: "E-MAIL DO GESTOR", value: "managerMail", sortable: false },
        { text: "AÇÕES", value: "actions", sortable: false },
      ],

      filters: [],
      data: [],
      total: 0,
      loading: false,
      filteringByName: false,
      filteringByCpf: false,
      filteringByEmail: false,
      error: null,

      currentPage: 1,
      perPage: 10,

      sortBy: "fullName",
      sortDesc: false,

      selectedUserId: null,
      showRemoveUserModal: false,

      offerClass: {},
    };
  },

  async created() {
    if (!this.offerClassId) {
      throw new Error("ID da turma não foi definido.");
    }

    await this.getOfferClass();
    await this.getData();
  },

  computed: {
    excludedUserIds() {
      return this.filters
        .filter((filter) => filter.type === "name")
        .map((filter) => filter.id);
    },

    excludedManagerIds() {
      return this.filters
        .filter((filter) => filter.type === "manager")
        .map((filter) => filter.id);
    },

    excludedCompanyIds() {
      return this.filters
        .filter((filter) => filter.type === "company")
        .map((filter) => filter.id);
    },

    /**
     * Returns a string representation of the total number of data.
     */
    totalDataInfo() {
      if (this.total === 0) return "Nenhum participante encontrado";
      if (this.total === 1) return "1 participante encontrado";

      return `${this.total} participantes encontrados`;
    },
  },

  watch: {
    perPage(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.currentPage = 1;

        this.getData();
      }
    },
    currentPage(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.getData();
      }
    },
  },

  methods: {
    /**
     * Gets offer class data
     *
     * @return {Promise<void>}
     */
    async getOfferClass() {
      try {
        this.loading = true;

        const response = await getClass(this.offerClassId);

        this.offerClass = response;
      } catch (error) {
        this.showErrorMessage(error);
      } finally {
        this.loading = false;
      }
    },

    /**
     * Fetches enrolled users from backend
     *
     * @returns {Promise<void>}
     */
    async getData() {
      try {
        this.loading = true;
        this.error = null;

        const params = {
          offerclassid: this.offerClassId,
          filters: {
            userids: [...this.excludedUserIds],
            managerids: [...this.excludedManagerIds],
            companyids: [...this.excludedCompanyIds],
          },
          page: this.currentPage,
          perpage: this.perPage,
          orderby: this.mapSortFieldToBackend(this.sortBy || "fullName"),
          direction: this.sortDesc ? "DESC" : "ASC",
        };

        const { waitlist: data, total } = await fetchWaitingListUsers(params);
        console.log(data);
        this.total = total;

        this.data = data.map((item) => {
          return {
            ...item,
            order: `${item.order}º`,
            fullName: item.fullname,
            companyName: item.companyname,
            managerName: item.managername,
            managerPhone: formatPhoneNumber(item.managerphone),
            managerMail: item.managermail,
          };
        });
      } catch (error) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    },

    /**
     * Gets an array of options for the name filter autocomplete component.
     *
     * @param {string} searchString - The search string to filter the enrolled users by.
     * @returns {Promise<void>}
     */
    async getNameOptions(searchString) {
      try {
        if (!searchString || searchString.length < 3) {
          this.nameOptions = [];

          return;
        }

        this.filteringByName = true;

        const response = await searchWaitingListUsers({
          offerclassid: this.offerClassId,
          fieldstring: "name",
          searchstring: searchString,
          excludeids: this.excludedUserIds,
        });

        this.nameOptions = response.map((user) => ({
          id: user.id,
          value: user.id,
          label: user.fullname,
        }));
      } catch (error) {
        this.nameOptions = [];
      } finally {
        this.filteringByName = false;
      }
    },

    /**
     * Gets an array of options for the manager filter autocomplete component.
     *
     * @param {string} searchString - The search string to filter the enrolled users by.
     * @returns {Promise<void>}
     */
    async getManagerOptions(searchString) {
      try {
        if (!searchString || searchString.length < 3) {
          this.managerOptions = [];

          return;
        }

        this.filteringByCpf = true;

        const response = await searchWaitingListUsers({
          offerclassid: this.offerClassId,
          fieldstring: "manager",
          searchstring: searchString,
          excludeids: this.excludedManagerIds,
        });

        this.managerOptions = response.map((user) => ({
          id: user.id,
          value: user.id,
          label: user.fullname,
        }));
      } catch (error) {
        this.managerOptions = [];
      } finally {
        this.filteringByCpf = false;
      }
    },

    /**
     * Gets an array of options for the company filter autocomplete component.
     *
     * @param {string} searchString - The search string to filter the enrolled users by.
     * @returns {Promise<void>}
     */
    async getCompanyOptions(searchString) {
      try {
        if (!searchString || searchString.length < 3) {
          this.companyOptions = [];
          return;
        }

        this.filteringByEmail = true;

        const response = await searchWaitingListUsers({
          offerclassid: this.offerClassId,
          fieldstring: "company",
          searchstring: searchString,
          excludeids: this.excludedCompanyIds,
        });

        this.companyOptions = response.map((user) => ({
          id: user.id,
          value: user.id,
          label: user.fullname,
        }));
      } catch (error) {
        this.companyOptions = [];
      } finally {
        this.filteringByEmail = false;
      }
    },

    // Métodos para selecionar opções dos dropdowns
    selectNameOption(selectedOptions) {
      selectedOptions.forEach((option) => {
        this.filters.push({
          id: option.id,
          value: option.value,
          label: option.label,
          type: "name",
        });
      });

      this.clearOptions();
      this.getData();
    },

    selectCpfOption(selectedOptions) {
      selectedOptions.forEach((option) => {
        this.filters.push({
          id: option.id,
          value: option.value,
          label: option.label,
          type: "manager",
        });
      });

      this.clearOptions();
      this.getData();
    },

    async selectEmailOption(selectedOptions) {
      selectedOptions.forEach((option) => {
        this.filters.push({
          id: option.id,
          value: option.value,
          label: option.label,
          type: "company",
        });
      });

      await this.clearOptions();
      this.getData();
    },

    clearOptions(field) {
      switch (field) {
        case "name":
          this.nameOptions = [];
          break;
        case "manager":
          this.managerOptions = [];
          break;
        case "company":
          this.companyOptions = [];
          break;
        default:
          this.nameOptions = [];
          this.managerOptions = [];
          this.companyOptions = [];
          break;
      }
    },

    /**
     * Remove um usuário da lista de filtros baseado no índice ou ID do usuário
     * @param {number|string} indexOrUserId - Índice do usuário na lista filters ou ID do usuário
     */
    removeFilter(userid) {
      const userIndex = this.filters.findIndex(
        (user) => user.id === userid || user.value === userid
      );

      if (userIndex !== -1) {
        this.filters.splice(userIndex, 1);
      }
      this.getData();
    },

    /**
     * Limpa todos os filtros aplicados e recarrega a lista de usuários
     */
    clearfilters() {
      this.filters = [];
      this.getData();
    },

    async handleTableSort({ sortBy, sortDesc }) {
      this.sortBy = sortBy;
      this.sortDesc = sortDesc;

      await this.getData();
    },

    mapSortFieldToBackend(frontendField) {
      const fieldMapping = {
        fullName: "fullname",
        companyName: "companyname",
        managerName: "managername",
        managerPhone: "managerphone",
        managerMail: "managermail",
        timeCreated: "timecreated",
      };

      return fieldMapping[frontendField] || "fullname";
    },

    async navigateToBack() {
      this.$router.push({
        name: "offer.edit",
        params: { id: this.offerClass.offerid },
      });
    },

    /**
     * Opens the remove user modal
     * @param {number} userid - ID of the user to be removed
     */
    requestRemoveUser(userid) {
      this.selectedUserId = userid;
      this.showRemoveUserModal = true;
    },

    /**
     * Removes the user from the waiting list
     */
    async removeUser() {
      try {
        await removeFromWaitingList({
          offerclassid: this.offerClassId,
          userid: this.selectedUserId,
        });

        this.data = this.data.filter((user) => user.id !== userid);
        this.showRemoveUserModal = false;
      } catch (error) {
        this.showErrorMessage(error.message || "Erro ao remover usuário.");
      }
    },
  },
};
</script>

<style src="@/assets/scss/RegisteredUsers.scss" lang="scss" scoped></style>
